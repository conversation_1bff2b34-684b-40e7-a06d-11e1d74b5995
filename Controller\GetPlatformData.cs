using System;


namespace PlatformApp.GetPlatformData
{
    public class ScrapingService : IHostedService, IDisposable
{
    private Timer? _timer ;
    private readonly ILogger<ScrapingService> _logger;

    public ScrapingService(ILogger<ScrapingService> logger)
    {
        _logger = logger;
    }

    // サービス開始時に呼ばれる
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("スクレイピングサービスを開始しました。");

        // 5秒後に開始し、その後30秒ごとに実行
        _timer = new Timer(DoWork, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(30));

        return Task.CompletedTask;
    }

    private void DoWork(object state)
    {
        _logger.LogInformation("スクレイピングを実行しています...");
        // ここにスクレイピングとデータベース更新のロジックを記述
        // 例: Webサイトからデータを取得し、データベースに保存
    }

    // サービス停止時に呼ばれる
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("スクレイピングサービスを停止しています。");
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}
    
}